<?php
require_once 'config.php';

// Check if it's a form submission with file
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle multipart form data
    $menu = $_POST['menu'] ?? '';
    $deskripsi = $_POST['deskripsi'] ?? '';
    $harga = $_POST['harga'] ?? 0;
    $id_kategori = $_POST['id_kategori'] ?? 0;

    // Validate required fields
    if (empty($menu) || empty($deskripsi) || empty($harga) || empty($id_kategori)) {
        echo json_encode(['success' => false, 'message' => 'Data menu tidak lengkap']);
        exit;
    }

    $gambar = null;

    // Handle image upload if provided
    if (isset($_FILES['gambar']) && $_FILES['gambar']['error'] === UPLOAD_ERR_OK) {
        $uploadResponse = handleImageUpload($_FILES['gambar']);
        if (!$uploadResponse['success']) {
            echo json_encode(['success' => false, 'message' => $uploadResponse['message']]);
            exit;
        }
        $gambar = $uploadResponse['filename'];
    }
}

// Function to handle image upload
function handleImageUpload($file) {
    $response = ['success' => false, 'message' => '', 'filename' => ''];

    try {
        $fileName = $file['name'];
        $fileTmpName = $file['tmp_name'];
        $fileSize = $file['size'];
        $fileError = $file['error'];

        // Get file extension
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        // Allowed extensions
        $allowed = ['jpg', 'jpeg', 'png', 'webp'];

        // Validate file extension
        if (!in_array($fileExt, $allowed)) {
            throw new Exception('Invalid file type');
        }

        // Validate file size (5MB max)
        if ($fileSize > 5000000) {
            throw new Exception('File is too large');
        }

        // Check for upload errors
        if ($fileError !== 0) {
            throw new Exception('Error uploading file');
        }

        // Generate unique filename
        $newFileName = uniqid('menu_', true) . '.' . $fileExt;

        // Upload directory path
        $uploadDir = __DIR__ . '/../uploads/';

        // Create directory if it doesn't exist
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        // Full path for the new file
        $uploadPath = $uploadDir . $newFileName;

        // Move uploaded file
        if (!move_uploaded_file($fileTmpName, $uploadPath)) {
            throw new Exception('Failed to move uploaded file');
        }

        $response['success'] = true;
        $response['filename'] = $newFileName;

    } catch (Exception $e) {
        $response['message'] = $e->getMessage();
    }

    return $response;
}

try {
    $conn = getConnection();

    $stmt = $conn->prepare("INSERT INTO menus (menu, deskripsi, gambar, harga, id_kategori, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())");
    $stmt->bind_param("sssdi", $menu, $deskripsi, $gambar, $harga, $id_kategori);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Menu berhasil ditambahkan']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal menambah menu: ' . $stmt->error]);
    }

    $stmt->close();
    $conn->close();

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}