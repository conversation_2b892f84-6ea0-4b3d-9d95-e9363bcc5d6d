<?php
require_once 'config.php';

try {
    $conn = getConnection();

    // Sample image URLs from Unsplash for food
    $foodImages = [
        'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop', // Pizza
        'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=300&fit=crop', // Burger
        'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=400&h=300&fit=crop', // Pasta
        'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=300&fit=crop', // Pancakes
        'https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=400&h=300&fit=crop', // Sandwich
        'https://images.unsplash.com/photo-1565958011703-44f9829ba187?w=400&h=300&fit=crop', // Salad
        'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400&h=300&fit=crop', // Steak
        'https://images.unsplash.com/photo-1574071318508-1cdbab80d002?w=400&h=300&fit=crop', // Soup
        'https://images.unsplash.com/photo-1551782450-17144efb9c50?w=400&h=300&fit=crop', // Tacos
        'https://images.unsplash.com/photo-1565299507177-b0ac66763828?w=400&h=300&fit=crop', // Fried Rice
        'https://images.unsplash.com/photo-1571091655789-405eb7a3a3a8?w=400&h=300&fit=crop', // Noodles
        'https://images.unsplash.com/photo-1565958011703-44f9829ba187?w=400&h=300&fit=crop', // Chicken
        'https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=400&h=300&fit=crop', // Fish
        'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400&h=300&fit=crop', // Dessert
        'https://images.unsplash.com/photo-1551782450-17144efb9c50?w=400&h=300&fit=crop', // Drinks
    ];

    // Get all menus without images
    $sql = "SELECT id, menu FROM menus WHERE gambar IS NULL OR gambar = ''";
    $result = $conn->query($sql);

    $updated = 0;
    $imageIndex = 0;

    if ($result->num_rows > 0) {
        while($row = $result->fetch_assoc()) {
            $menuId = $row['id'];
            $imageUrl = $foodImages[$imageIndex % count($foodImages)];
            
            // Update menu with image URL
            $updateSql = "UPDATE menus SET gambar = ? WHERE id = ?";
            $stmt = $conn->prepare($updateSql);
            $stmt->bind_param("si", $imageUrl, $menuId);
            
            if ($stmt->execute()) {
                $updated++;
                echo "Updated menu ID {$menuId} with image: {$imageUrl}<br>";
            }
            
            $stmt->close();
            $imageIndex++;
        }
    }

    echo json_encode([
        'success' => true,
        'message' => "Successfully updated {$updated} menu items with images"
    ]);

    $conn->close();

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
