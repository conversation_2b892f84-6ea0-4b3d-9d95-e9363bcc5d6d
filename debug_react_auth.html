<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug React Authentication</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .log-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-warning text-dark">
                        <h3 class="mb-0">
                            <i class="bi bi-bug-fill me-2"></i>
                            Debug React Authentication
                        </h3>
                    </div>
                    <div class="card-body">
                        
                        <!-- Step 1: Check Services -->
                        <h5>1️⃣ Service Status Check</h5>
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <button class="btn btn-info w-100" onclick="checkXAMPP()">
                                    <i class="bi bi-server me-2"></i>Check XAMPP
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary w-100" onclick="checkReactApp()">
                                    <i class="bi bi-react me-2"></i>Check React App
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-success w-100" onclick="checkDatabase()">
                                    <i class="bi bi-database me-2"></i>Check Database
                                </button>
                            </div>
                        </div>
                        
                        <!-- Step 2: Test APIs -->
                        <h5>2️⃣ API Testing</h5>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Login API Test</h6>
                                    </div>
                                    <div class="card-body">
                                        <form id="loginTestForm">
                                            <div class="mb-2">
                                                <input type="text" class="form-control form-control-sm" id="loginUsername" placeholder="Username" value="admin">
                                            </div>
                                            <div class="mb-2">
                                                <input type="password" class="form-control form-control-sm" id="loginPassword" placeholder="Password" value="admin123">
                                            </div>
                                            <button type="submit" class="btn btn-primary btn-sm w-100">Test Login</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Register API Test</h6>
                                    </div>
                                    <div class="card-body">
                                        <form id="registerTestForm">
                                            <div class="mb-2">
                                                <input type="text" class="form-control form-control-sm" id="regName" placeholder="Full Name" value="Test User">
                                            </div>
                                            <div class="mb-2">
                                                <input type="text" class="form-control form-control-sm" id="regUsername" placeholder="Username" value="">
                                            </div>
                                            <div class="mb-2">
                                                <input type="email" class="form-control form-control-sm" id="regEmail" placeholder="Email" value="">
                                            </div>
                                            <div class="mb-2">
                                                <input type="password" class="form-control form-control-sm" id="regPassword" placeholder="Password" value="test123">
                                            </div>
                                            <button type="submit" class="btn btn-success btn-sm w-100">Test Register</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 3: Test Different Endpoints -->
                        <h5>3️⃣ Endpoint Testing</h5>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary btn-sm w-100" onclick="testEndpoint('/api/login.php', 'POST')">
                                    Direct API
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-success btn-sm w-100" onclick="testEndpoint('http://localhost/project-react-resto/api/login.php', 'POST')">
                                    Full URL
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning btn-sm w-100" onclick="testCORS()">
                                    Test CORS
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info btn-sm w-100" onclick="testProxy()">
                                    Test Proxy
                                </button>
                            </div>
                        </div>
                        
                        <!-- Results -->
                        <h5>📊 Test Results</h5>
                        <div id="results" class="log-box">
                            Click any test button to see results...
                        </div>
                        
                        <!-- Quick Actions -->
                        <h5>🚀 Quick Actions</h5>
                        <div class="row">
                            <div class="col-md-2">
                                <a href="diagnose_auth.php" class="btn btn-danger btn-sm w-100" target="_blank">
                                    Full Diagnosis
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="http://localhost:5173" class="btn btn-primary btn-sm w-100" target="_blank">
                                    React App
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="http://localhost:5173/login" class="btn btn-info btn-sm w-100" target="_blank">
                                    Login Page
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="http://localhost/phpmyadmin" class="btn btn-warning btn-sm w-100" target="_blank">
                                    phpMyAdmin
                                </a>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-secondary btn-sm w-100" onclick="clearResults()">
                                    Clear Results
                                </button>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-success btn-sm w-100" onclick="runAllTests()">
                                    Run All Tests
                                </button>
                            </div>
                        </div>
                        
                        <!-- Instructions -->
                        <div class="mt-4 p-3 bg-info text-white rounded">
                            <h6><i class="bi bi-info-circle me-2"></i>Instructions</h6>
                            <ol class="mb-0">
                                <li>First run "Full Diagnosis" to fix database issues</li>
                                <li>Make sure XAMPP Apache and MySQL are running</li>
                                <li>Test the APIs using the forms above</li>
                                <li>Check React app at localhost:5173</li>
                                <li>Look at browser console (F12) for errors</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const resultsDiv = document.getElementById('results');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            resultsDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            if (type === 'error') {
                resultsDiv.className = 'log-box error';
            } else if (type === 'success') {
                resultsDiv.className = 'log-box success';
            } else if (type === 'warning') {
                resultsDiv.className = 'log-box warning';
            }
        }
        
        function clearResults() {
            resultsDiv.textContent = 'Results cleared...\n';
            resultsDiv.className = 'log-box';
        }
        
        async function checkXAMPP() {
            log('Checking XAMPP services...');
            try {
                const response = await fetch('http://localhost/dashboard/', { mode: 'no-cors' });
                log('XAMPP Apache is running', 'success');
            } catch (error) {
                log('XAMPP Apache may not be running: ' + error.message, 'error');
            }
        }
        
        async function checkReactApp() {
            log('Checking React app...');
            try {
                const response = await fetch('http://localhost:5173/');
                if (response.ok) {
                    log('React app is running on port 5173', 'success');
                } else {
                    log('React app responded with status: ' + response.status, 'warning');
                }
            } catch (error) {
                log('React app is not running: ' + error.message, 'error');
            }
        }
        
        async function checkDatabase() {
            log('Checking database connection...');
            try {
                const response = await fetch('/api/login.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'test', password: 'test' })
                });
                
                if (response.ok) {
                    log('Database API is accessible', 'success');
                } else {
                    log('Database API returned status: ' + response.status, 'warning');
                }
            } catch (error) {
                log('Database API error: ' + error.message, 'error');
            }
        }
        
        async function testEndpoint(url, method) {
            log(`Testing ${method} ${url}...`);
            try {
                const response = await fetch(url, {
                    method: method,
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                
                const data = await response.json();
                log(`Response: ${JSON.stringify(data, null, 2)}`, data.success ? 'success' : 'error');
            } catch (error) {
                log(`Endpoint test failed: ${error.message}`, 'error');
            }
        }
        
        async function testCORS() {
            log('Testing CORS...');
            try {
                const response = await fetch('http://localhost/project-react-resto/api/login.php', {
                    method: 'OPTIONS'
                });
                log('CORS preflight successful', 'success');
            } catch (error) {
                log('CORS test failed: ' + error.message, 'error');
            }
        }
        
        async function testProxy() {
            log('Testing Vite proxy...');
            try {
                const response = await fetch('/api/login.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                
                const data = await response.json();
                log('Proxy test result: ' + JSON.stringify(data), data.success ? 'success' : 'warning');
            } catch (error) {
                log('Proxy test failed: ' + error.message, 'error');
            }
        }
        
        // Form handlers
        document.getElementById('loginTestForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            
            log(`Testing login with ${username}/${password}...`);
            
            try {
                const response = await fetch('/api/login.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`Login successful! User: ${data.data.user.name}, Role: ${data.data.user.role}`, 'success');
                } else {
                    log(`Login failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`Login test error: ${error.message}`, 'error');
            }
        });
        
        document.getElementById('registerTestForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // Generate unique values
            const timestamp = Date.now();
            const name = document.getElementById('regName').value;
            const username = document.getElementById('regUsername').value || `testuser${timestamp}`;
            const email = document.getElementById('regEmail').value || `test${timestamp}@example.com`;
            const password = document.getElementById('regPassword').value;
            
            // Update form fields
            document.getElementById('regUsername').value = username;
            document.getElementById('regEmail').value = email;
            
            log(`Testing register with ${username}/${email}...`);
            
            try {
                const response = await fetch('/api/register.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ name, username, email, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`Register successful! User: ${data.data.user.name}`, 'success');
                } else {
                    log(`Register failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`Register test error: ${error.message}`, 'error');
            }
        });
        
        async function runAllTests() {
            clearResults();
            log('Running comprehensive tests...');
            
            await checkXAMPP();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await checkReactApp();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await checkDatabase();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testCORS();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testProxy();
            
            log('All tests completed!', 'success');
        }
    </script>
</body>
</html>
