/* Home Page Styles - Prismatic Design */

.home-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: calc(100vh - 100px);
  position: relative;
  overflow-x: hidden;
  margin: -2rem;
  padding: 2rem;
}

/* Hero Section */
.hero-section {
  padding: 120px 0;
  position: relative;
  z-index: 1;
  background:
    radial-gradient(circle at 20% 80%, rgba(240, 147, 251, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(67, 233, 123, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(79, 172, 254, 0.3) 0%, transparent 50%);
}

.hero-content {
  color: white;
  text-align: left;
}

.hero-title {
  font-size: 4.5rem;
  font-weight: 800;
  margin-bottom: 2rem;
  line-height: 1.1;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.text-gradient {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite alternate;
}

@keyframes gradientShift {
  0% {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  100% {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.6;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.glass-alert {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  color: white;
}

.hero-actions {
  margin-top: 2rem;
}

.hero-actions .btn {
  border-radius: 50px;
  padding: 15px 30px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.4s ease;
  text-decoration: none;
}

.hero-actions .btn-primary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
}

.hero-actions .btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(240, 147, 251, 0.6);
}

.hero-actions .btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.hero-actions .btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
}

.hero-image img {
  border-radius: 25px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
  transition: transform 0.6s ease;
}

.hero-image img:hover {
  transform: scale(1.05) rotate(2deg);
}

/* Popular Menu Section */
.popular-menu-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  position: relative;
}

.popular-menu-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.section-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 3rem;
}

.popular-menu-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  overflow: hidden;
  transition: all 0.5s ease;
  height: 100%;
  position: relative;
}

.popular-menu-card:hover {
  transform: translateY(-15px) scale(1.03);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
}

.menu-image-container {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.menu-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.popular-menu-card:hover .menu-image {
  transform: scale(1.15);
}

.menu-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
  z-index: 2;
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.menu-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.menu-rating {
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(102, 126, 234, 0.1);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.9rem;
  color: #667eea;
}

.menu-description {
  font-size: 0.95rem;
  color: #718096;
  margin-bottom: 20px;
  line-height: 1.5;
}

.menu-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

/* Stats Section */
.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px 20px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #f093fb 0%, #f5576c 50%, #43e97b 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.5s ease;
}

.stat-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-number {
  font-size: 3.5rem;
  font-weight: 800;
  color: white;
  display: block;
  margin-bottom: 10px;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.8rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-actions .btn {
    padding: 12px 25px;
    font-size: 1rem;
    margin-bottom: 10px;
  }

  .section-title {
    font-size: 2.2rem;
  }

  .menu-image-container {
    height: 200px;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .stat-label {
    font-size: 1rem;
  }
}

@media (max-width: 576px) {
  .hero-section {
    padding: 80px 0;
  }

  .hero-title {
    font-size: 2.2rem;
  }

  .popular-menu-section {
    padding: 60px 0;
  }

  .section-title {
    font-size: 1.8rem;
  }
}

/* Animation Classes */
.fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scale-in {
  animation: scaleIn 0.6s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Floating Elements */
.floating-element {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
