import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { <PERSON>, But<PERSON>, Alert, Spinner } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

const Login = () => {
  const [formData, setFormData] = useState({ username: '', password: '' });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();
  const { login, loading, isAuthenticated, user } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      if (user.role === 'admin') {
        navigate('/admin');
      } else {
        navigate('/');
      }
    }
  }, [isAuthenticated, user, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    console.log('🚀 Form submitted with data:', formData);

    // Validation
    if (!formData.username.trim() || !formData.password.trim()) {
      console.log('❌ Validation failed: empty fields');
      setError('Username dan password harus diisi');
      return;
    }

    console.log('✅ Validation passed, calling login...');

    try {
      const result = await login(formData);
      console.log('📨 Login result:', result);

      if (result.success) {
        console.log('🎉 Login successful!');
        setSuccess('Login berhasil! Mengalihkan...');

        // Redirect based on role
        setTimeout(() => {
          const userRole = result.user.role;
          console.log('🔄 Redirecting user with role:', userRole);

          switch (userRole) {
            case 'admin':
              console.log('➡️ Navigating to /admin');
              navigate('/admin');
              break;
            case 'koki':
              console.log('➡️ Navigating to /koki');
              navigate('/koki');
              break;
            case 'kasir':
              console.log('➡️ Navigating to /kasir');
              navigate('/kasir');
              break;
            case 'user':
            default:
              console.log('➡️ Navigating to /');
              navigate('/');
              break;
          }
        }, 1000);
      } else {
        console.log('❌ Login failed:', result.error);
        setError(result.error || 'Login gagal');
      }
    } catch (err) {
      console.error('💥 Login exception:', err);
      setError('Terjadi kesalahan saat login');
    }
  };

  return (
    <div style={{ backgroundColor: '#f8f9fa', minHeight: '100vh' }}>
      {/* Simple Header */}
      <div style={{ backgroundColor: 'white', padding: '60px 20px', borderBottom: '1px solid #e9ecef' }}>
        <div style={{ maxWidth: '500px', margin: '0 auto' }}>
          <div className="text-center">
            <h1 style={{ fontSize: '2.5rem', fontWeight: '300', color: '#333', marginBottom: '10px' }}>
              Welcome Back
            </h1>
            <p style={{ fontSize: '1.1rem', color: '#666', marginBottom: '0' }}>
              Sign in to your restaurant account
            </p>
          </div>
        </div>
      </div>

      {/* Login Form Section */}
      <div style={{ padding: '60px 20px' }}>
        <div style={{ maxWidth: '500px', margin: '0 auto' }}>
          {/* Alerts */}
          {error && (
            <Alert variant="danger" style={{ border: 'none', borderRadius: '8px', marginBottom: '30px' }}>
              {error}
            </Alert>
          )}
          {success && (
            <Alert variant="success" style={{ border: 'none', borderRadius: '8px', marginBottom: '30px' }}>
              {success}
            </Alert>
          )}

          {/* Login Form */}
          <div style={{ backgroundColor: 'white', padding: '40px', borderRadius: '12px', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
            <Form onSubmit={handleSubmit}>
              <Form.Group className="mb-3">
                <Form.Label style={{ fontWeight: '500', color: '#333', marginBottom: '8px' }}>
                  Username
                </Form.Label>
                <Form.Control
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  placeholder="Enter your username"
                  style={{
                    padding: '12px 16px',
                    border: '1px solid #e9ecef',
                    borderRadius: '8px',
                    fontSize: '1rem'
                  }}
                  required
                />
              </Form.Group>

              <Form.Group className="mb-4">
                <Form.Label style={{ fontWeight: '500', color: '#333', marginBottom: '8px' }}>
                  Password
                </Form.Label>
                <Form.Control
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="Enter your password"
                  style={{
                    padding: '12px 16px',
                    border: '1px solid #e9ecef',
                    borderRadius: '8px',
                    fontSize: '1rem'
                  }}
                  required
                />
              </Form.Group>

              <Button
                type="submit"
                style={{
                  width: '100%',
                  padding: '12px',
                  backgroundColor: '#007bff',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  fontWeight: '500'
                }}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Spinner
                      as="span"
                      animation="border"
                      size="sm"
                      role="status"
                      aria-hidden="true"
                      className="me-2"
                    />
                    Signing in...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>
            </Form>

            {/* Register Link */}
            <div className="text-center mt-4">
              <p style={{ color: '#666', marginBottom: '15px' }}>Don't have an account?</p>
              <Link
                to="/register"
                style={{
                  display: 'inline-block',
                  padding: '10px 20px',
                  color: '#007bff',
                  textDecoration: 'none',
                  border: '1px solid #007bff',
                  borderRadius: '8px',
                  fontWeight: '500'
                }}
              >
                Create New Account
              </Link>
            </div>
          </div>

          {/* Demo Credentials */}
          <div style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '12px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            marginTop: '30px'
          }}>
            <h6 style={{ fontSize: '1.1rem', fontWeight: '500', color: '#333', marginBottom: '20px' }}>
              Demo Credentials
            </h6>
            <div className="row g-3">
              <div className="col-6">
                <div style={{ padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                  <div style={{ fontWeight: '500', color: '#333', marginBottom: '5px' }}>Admin</div>
                  <div style={{ fontSize: '0.9rem', color: '#666' }}>
                    <code>admin</code> / <code>admin123</code>
                  </div>
                </div>
              </div>
              <div className="col-6">
                <div style={{ padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                  <div style={{ fontWeight: '500', color: '#333', marginBottom: '5px' }}>Kasir</div>
                  <div style={{ fontSize: '0.9rem', color: '#666' }}>
                    <code>kasir</code> / <code>kasir123</code>
                  </div>
                </div>
              </div>
              <div className="col-6">
                <div style={{ padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                  <div style={{ fontWeight: '500', color: '#333', marginBottom: '5px' }}>Koki</div>
                  <div style={{ fontSize: '0.9rem', color: '#666' }}>
                    <code>koki</code> / <code>koki123</code>
                  </div>
                </div>
              </div>
              <div className="col-6">
                <div style={{ padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                  <div style={{ fontWeight: '500', color: '#333', marginBottom: '5px' }}>User</div>
                  <div style={{ fontSize: '0.9rem', color: '#666' }}>
                    <code>user</code> / <code>user123</code>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;