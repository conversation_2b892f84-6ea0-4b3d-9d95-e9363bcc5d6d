import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

const Login = () => {
  const [formData, setFormData] = useState({ username: '', password: '' });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();
  const { login, loading, isAuthenticated, user } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      if (user.role === 'admin') {
        navigate('/admin');
      } else {
        navigate('/');
      }
    }
  }, [isAuthenticated, user, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validation
    if (!formData.username.trim() || !formData.password.trim()) {
      setError('Username dan password harus diisi');
      return;
    }

    try {
      const result = await login(formData);

      if (result.success) {
        setSuccess('Login berhasil! Mengalihkan...');

        // Redirect based on role
        setTimeout(() => {
          if (result.user.role === 'admin') {
            navigate('/admin');
          } else {
            navigate('/');
          }
        }, 1000);
      } else {
        setError(result.error || 'Login gagal');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('Terjadi kesalahan saat login');
    }
  };

  return (
    <div className="min-vh-100 d-flex align-items-center" style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <Container>
        <Row className="justify-content-center">
          <Col md={8} lg={6} xl={5}>
            <Card className="border-0 shadow-lg">
              <Card.Body className="p-5">
                {/* Header */}
                <div className="text-center mb-4">
                  <div className="mb-3">
                    <div className="bg-primary bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center"
                         style={{ width: '80px', height: '80px' }}>
                      <i className="bi bi-shop text-white" style={{ fontSize: '2rem' }}></i>
                    </div>
                  </div>
                  <h2 className="fw-bold text-dark mb-2">Welcome Back!</h2>
                  <p className="text-muted mb-0">Sign in to your restaurant account</p>
                </div>

                {/* Alerts */}
                {error && (
                  <Alert variant="danger" className="border-0 shadow-sm">
                    <i className="bi bi-exclamation-triangle me-2"></i>
                    {error}
                  </Alert>
                )}
                {success && (
                  <Alert variant="success" className="border-0 shadow-sm">
                    <i className="bi bi-check-circle me-2"></i>
                    {success}
                  </Alert>
                )}

                {/* Login Form */}
                <Form onSubmit={handleSubmit}>
                  <Form.Group className="mb-3">
                    <Form.Label className="fw-semibold text-dark">
                      <i className="bi bi-person me-2"></i>Username
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      placeholder="Enter your username"
                      className="form-control-lg border-0 shadow-sm"
                      style={{ backgroundColor: '#f8f9fa' }}
                      required
                    />
                  </Form.Group>

                  <Form.Group className="mb-4">
                    <Form.Label className="fw-semibold text-dark">
                      <i className="bi bi-lock me-2"></i>Password
                    </Form.Label>
                    <Form.Control
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      placeholder="Enter your password"
                      className="form-control-lg border-0 shadow-sm"
                      style={{ backgroundColor: '#f8f9fa' }}
                      required
                    />
                  </Form.Group>

                  <Button
                    type="submit"
                    className="w-100 btn-lg fw-semibold border-0 shadow-sm"
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                    }}
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Signing in...
                      </>
                    ) : (
                      <>
                        <i className="bi bi-box-arrow-in-right me-2"></i>
                        Sign In
                      </>
                    )}
                  </Button>
                </Form>

                {/* Divider */}
                <div className="text-center my-4">
                  <hr className="my-3" />
                  <span className="text-muted bg-white px-3">or</span>
                </div>

                {/* Register Link */}
                <div className="text-center">
                  <p className="text-muted mb-2">Don't have an account?</p>
                  <Link
                    to="/register"
                    className="btn btn-outline-primary btn-lg fw-semibold w-100"
                  >
                    <i className="bi bi-person-plus me-2"></i>
                    Create New Account
                  </Link>
                </div>

                {/* Demo Credentials */}
                <div className="mt-4 p-3 bg-light rounded">
                  <h6 className="fw-bold text-dark mb-2">
                    <i className="bi bi-info-circle me-2"></i>Demo Credentials
                  </h6>
                  <div className="row g-2">
                    <div className="col-6">
                      <small className="d-block">
                        <strong>Admin:</strong><br />
                        <code>admin</code> / <code>admin123</code>
                      </small>
                    </div>
                    <div className="col-6">
                      <small className="d-block">
                        <strong>Kasir:</strong><br />
                        <code>kasir</code> / <code>kasir123</code>
                      </small>
                    </div>
                    <div className="col-6">
                      <small className="d-block">
                        <strong>Koki:</strong><br />
                        <code>koki</code> / <code>koki123</code>
                      </small>
                    </div>
                    <div className="col-6">
                      <small className="d-block">
                        <strong>User:</strong><br />
                        <code>user</code> / <code>user123</code>
                      </small>
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default Login;