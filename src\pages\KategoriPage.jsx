import React, { useState, useEffect } from 'react';
import { kategoriService } from '../services/api';

const KategoriPage = () => {
  const [kategori, setKategori] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchKategori();
  }, []);

  const fetchKategori = async () => {
    try {
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout: Server tidak merespon')), 10000)
      );

      const dataPromise = kategoriService.getAllKategori();
      const data = await Promise.race([dataPromise, timeoutPromise]);

      if (!data) {
        throw new Error('Data kategori kosong');
      }

      setKategori(data);
      setError(null);
    } catch (err) {
      let errorMessage = 'Gagal mengambil data kategori. ';
      if (err.message.includes('Timeout')) {
        errorMessage += 'Server tidak merespon, silakan coba lagi nanti.';
      } else if (err.response?.status === 404) {
        errorMessage += 'Data tidak ditemukan.';
      } else if (!navigator.onLine) {
        errorMessage += 'Periksa koneksi internet Anda.';
      } else {
        errorMessage += 'Terjadi kesalahan, silakan coba lagi.';
      }
      setError(errorMessage);
      console.error('Error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );
  }

  return (
    <div style={{ backgroundColor: '#f8f9fa', minHeight: '100vh', padding: '20px' }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '30px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '30px'
        }}>
          <h2 style={{ margin: 0, fontWeight: '300', color: '#333' }}>
            Daftar Kategori Menu
          </h2>
          <button style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            fontSize: '14px',
            cursor: 'pointer'
          }}>
            Tambah Kategori Baru
          </button>
        </div>

        {loading ? (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <div style={{
              display: 'inline-block',
              width: '40px',
              height: '40px',
              border: '4px solid #f3f3f3',
              borderTop: '4px solid #007bff',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }}></div>
            <p style={{ marginTop: '15px', color: '#666' }}>Memuat data...</p>
          </div>
        ) : error ? (
          <div style={{
            backgroundColor: '#f8d7da',
            color: '#721c24',
            padding: '15px',
            borderRadius: '6px',
            border: '1px solid #f5c6cb'
          }}>
            {error}
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
              backgroundColor: 'white'
            }}>
              <thead>
                <tr style={{ backgroundColor: '#f8f9fa' }}>
                  <th style={{
                    padding: '15px',
                    textAlign: 'left',
                    borderBottom: '2px solid #dee2e6',
                    fontWeight: '500',
                    color: '#495057',
                    width: '10%'
                  }}>
                    No
                  </th>
                  <th style={{
                    padding: '15px',
                    textAlign: 'left',
                    borderBottom: '2px solid #dee2e6',
                    fontWeight: '500',
                    color: '#495057',
                    width: '40%'
                  }}>
                    Nama Kategori
                  </th>
                  <th style={{
                    padding: '15px',
                    textAlign: 'left',
                    borderBottom: '2px solid #dee2e6',
                    fontWeight: '500',
                    color: '#495057',
                    width: '50%'
                  }}>
                    Keterangan
                  </th>
                </tr>
              </thead>
              <tbody>
                {kategori.length > 0 ? (
                  kategori.map((item, index) => (
                    <tr key={item.id} style={{
                      borderBottom: '1px solid #dee2e6',
                      transition: 'background-color 0.2s ease'
                    }}
                    onMouseEnter={(e) => e.target.parentNode.style.backgroundColor = '#f8f9fa'}
                    onMouseLeave={(e) => e.target.parentNode.style.backgroundColor = 'transparent'}
                    >
                      <td style={{ padding: '15px', color: '#495057' }}>
                        {index + 1}
                      </td>
                      <td style={{ padding: '15px', color: '#495057', fontWeight: '500' }}>
                        {item.nama_kategori}
                      </td>
                      <td style={{ padding: '15px', color: '#6c757d' }}>
                        {item.keterangan}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="3" style={{
                      padding: '40px',
                      textAlign: 'center',
                      color: '#6c757d',
                      fontStyle: 'italic'
                    }}>
                      Tidak ada data kategori
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default KategoriPage;
