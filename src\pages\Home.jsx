import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Link } from 'react-router-dom';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { getPopularItems, formatPrice } from '../data/menuData';

const Home = () => {
  const { user, isAuthenticated, isAdmin } = useAuth();
  const popularItems = getPopularItems().slice(0, 6); // Get top 6 popular items

  return (
    <div style={{ backgroundColor: '#f8f9fa', minHeight: '100vh' }}>
      {/* Simple Header */}
      <div style={{ backgroundColor: 'white', padding: '60px 0', borderBottom: '1px solid #e9ecef' }}>
        <Container>
          <Row>
            <Col className="text-center">
              <h1 style={{ fontSize: '2.5rem', fontWeight: '300', color: '#333', marginBottom: '20px' }}>
                Prismatic Restaurant
              </h1>
              <p style={{ fontSize: '1.1rem', color: '#666', marginBottom: '30px' }}>
                Simple. Fresh. Delicious.
              </p>

              {/* User Status */}
              {isAuthenticated ? (
                <div style={{
                  backgroundColor: '#e8f5e8',
                  padding: '15px',
                  borderRadius: '8px',
                  marginBottom: '30px',
                  display: 'inline-block'
                }}>
                  <span style={{ color: '#155724' }}>
                    Welcome, <strong>{user?.name}</strong> ({user?.role})
                  </span>
                  {isAdmin() && (
                    <div style={{ marginTop: '10px' }}>
                      <Link to="/admin" className="btn btn-sm btn-success">
                        Admin Panel
                      </Link>
                    </div>
                  )}
                </div>
              ) : (
                <div style={{
                  backgroundColor: '#d1ecf1',
                  padding: '15px',
                  borderRadius: '8px',
                  marginBottom: '30px',
                  display: 'inline-block'
                }}>
                  <span style={{ color: '#0c5460' }}>
                    <Link to="/login" style={{ textDecoration: 'none', marginRight: '10px' }}>Login</Link>
                    or
                    <Link to="/register" style={{ textDecoration: 'none', marginLeft: '10px' }}>Register</Link>
                  </span>
                </div>
              )}

              <div>
                <Link to="/menu" className="btn btn-primary btn-lg me-3" style={{ borderRadius: '25px' }}>
                  View Menu
                </Link>
                <Button variant="outline-primary" size="lg" style={{ borderRadius: '25px' }}>
                  Make Reservation
                </Button>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Menu Section */}
      <div style={{ padding: '60px 0' }}>
        <Container>
          <Row className="mb-5">
            <Col className="text-center">
              <h2 style={{ fontSize: '2rem', fontWeight: '300', color: '#333', marginBottom: '10px' }}>
                Popular Menu
              </h2>
              <p style={{ color: '#666' }}>Our customers' favorite dishes</p>
            </Col>
          </Row>

          <Row>
            {popularItems.map(item => (
              <Col lg={4} md={6} key={item.id} className="mb-4">
                <Card style={{
                  border: 'none',
                  borderRadius: '12px',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                  height: '100%'
                }}>
                  <div style={{ position: 'relative', overflow: 'hidden', borderRadius: '12px 12px 0 0' }}>
                    <img
                      src={item.image}
                      alt={item.name}
                      style={{
                        width: '100%',
                        height: '200px',
                        objectFit: 'cover'
                      }}
                    />
                  </div>
                  <Card.Body style={{ padding: '20px' }}>
                    <h5 style={{ fontSize: '1.2rem', fontWeight: '500', color: '#333', marginBottom: '10px' }}>
                      {item.name}
                    </h5>
                    <p style={{ color: '#666', fontSize: '0.9rem', marginBottom: '15px' }}>
                      {item.description}
                    </p>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontSize: '1.1rem', fontWeight: '600', color: '#007bff' }}>
                        {formatPrice(item.price)}
                      </span>
                      <Button
                        variant="outline-primary"
                        size="sm"
                        style={{ borderRadius: '20px', padding: '5px 15px' }}
                      >
                        Order
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>

          <Row>
            <Col className="text-center mt-4">
              <Link to="/menu" className="btn btn-outline-primary btn-lg" style={{ borderRadius: '25px' }}>
                View All Menu
              </Link>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Simple Stats */}
      <div style={{ backgroundColor: 'white', padding: '40px 0', borderTop: '1px solid #e9ecef' }}>
        <Container>
          <Row>
            <Col md={3} className="text-center mb-3">
              <div style={{ padding: '20px' }}>
                <div style={{ fontSize: '2rem', fontWeight: '300', color: '#007bff' }}>500+</div>
                <div style={{ color: '#666', fontSize: '0.9rem' }}>Happy Customers</div>
              </div>
            </Col>
            <Col md={3} className="text-center mb-3">
              <div style={{ padding: '20px' }}>
                <div style={{ fontSize: '2rem', fontWeight: '300', color: '#007bff' }}>35+</div>
                <div style={{ color: '#666', fontSize: '0.9rem' }}>Menu Items</div>
              </div>
            </Col>
            <Col md={3} className="text-center mb-3">
              <div style={{ padding: '20px' }}>
                <div style={{ fontSize: '2rem', fontWeight: '300', color: '#007bff' }}>5</div>
                <div style={{ color: '#666', fontSize: '0.9rem' }}>Categories</div>
              </div>
            </Col>
            <Col md={3} className="text-center mb-3">
              <div style={{ padding: '20px' }}>
                <div style={{ fontSize: '2rem', fontWeight: '300', color: '#007bff' }}>4.8</div>
                <div style={{ color: '#666', fontSize: '0.9rem' }}>Average Rating</div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </div>
  );
};

export default Home;
