<?php
require_once 'config.php';

try {
    // Create connection using config
    $conn = getConnection();

    // Get menu with kategori information
    $sql = "
        SELECT
            m.id as id_menu,
            m.menu,
            m.deskripsi,
            m.harga,
            m.id_kategori,
            m.gambar,
            m.created_at,
            k.nama as kategori_nama
        FROM menus m
        LEFT JOIN kategori k ON m.id_kategori = k.id
        ORDER BY m.menu ASC
    ";

    $result = $conn->query($sql);
    $menus = [];

    if ($result->num_rows > 0) {
        while($row = $result->fetch_assoc()) {
            // Add full URL to image if exists
            $gambar_url = null;
            if ($row['gambar']) {
                // Check if it's already a full URL (from sample data)
                if (strpos($row['gambar'], 'http') === 0) {
                    $gambar_url = $row['gambar'];
                } else {
                    // Local uploaded file
                    $gambar_url = 'http://' . $_SERVER['HTTP_HOST'] . '/project-react-resto/uploads/' . $row['gambar'];
                }
            }

            $menus[] = [
                'id_menu' => (int)$row['id_menu'],
                'menu' => $row['menu'],
                'deskripsi' => $row['deskripsi'],
                'harga' => (float)$row['harga'],
                'id_kategori' => (int)$row['id_kategori'],
                'kategori_nama' => $row['kategori_nama'],
                'gambar' => $row['gambar'],
                'gambar_url' => $gambar_url,
                'created_at' => $row['created_at']
            ];
        }
    }

    echo json_encode([
        'success' => true,
        'data' => $menus,
        'total' => count($menus)
    ]);

    $conn->close();

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}