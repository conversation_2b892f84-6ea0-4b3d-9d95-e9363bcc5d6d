/* Orders Page Styles */
.orders-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.text-gradient {
  background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

/* Filter Section */
.filter-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-filter {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 15px;
  padding: 12px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-filter:focus {
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
  background: white;
}

.btn-refresh {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
  border-radius: 15px;
  padding: 12px 24px;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-refresh:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
  color: white;
}

/* Loading Spinner */
.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-state h3 {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
}

/* Orders Grid */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 25px;
}

/* Order Card */
.order-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.order-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* Order Header */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.order-id {
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
}

/* Status Badges */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-warning {
  background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
  color: #d63031;
}

.badge-info {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
}

.badge-primary {
  background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
  color: white;
}

.badge-success {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: white;
}

.badge-danger {
  background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
  color: white;
}

.badge-secondary {
  background: linear-gradient(135deg, #b2bec3 0%, #636e72 100%);
  color: white;
}

/* Order Content */
.order-content {
  margin-bottom: 20px;
}

.order-menu {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 15px;
}

.menu-image {
  flex-shrink: 0;
}

.menu-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-details h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.menu-quantity {
  color: #666;
  margin-bottom: 5px;
  font-weight: 500;
}

.menu-price {
  color: #e74c3c;
  font-weight: 700;
  font-size: 1.1rem;
}

/* Customer Info */
.customer-info {
  background: rgba(79, 172, 254, 0.1);
  padding: 15px;
  border-radius: 12px;
  border-left: 4px solid #4facfe;
}

.customer-info h5 {
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.customer-info p {
  margin-bottom: 5px;
  color: #555;
  font-size: 0.95rem;
}

/* Order Footer */
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 2px solid rgba(0, 0, 0, 0.1);
}

.order-date {
  color: #666;
  font-size: 0.9rem;
}

.order-total {
  color: #e74c3c;
  font-size: 1.2rem;
  font-weight: 700;
}

/* Responsive Design */
@media (max-width: 768px) {
  .orders-grid {
    grid-template-columns: 1fr;
  }
  
  .hero-title {
    font-size: 2.2rem;
  }
  
  .order-card {
    padding: 20px;
  }
  
  .order-menu {
    flex-direction: column;
    text-align: center;
  }
  
  .menu-image {
    align-self: center;
  }
  
  .order-footer {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .orders-page {
    padding: 20px 10px;
  }
  
  .hero-title {
    font-size: 1.8rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .filter-section {
    padding: 15px;
  }
  
  .order-card {
    padding: 15px;
  }
}
