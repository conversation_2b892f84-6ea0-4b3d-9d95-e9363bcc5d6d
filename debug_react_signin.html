<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug React Sign In Issue</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h3 class="mb-0">
                    <i class="bi bi-bug-fill me-2"></i>
                    Debug React Sign In Issue
                </h3>
            </div>
            <div class="card-body">
                
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Problem:</strong> Halaman login React tampil tapi tombol Sign In tidak berfungsi
                </div>
                
                <!-- Quick Test -->
                <h5>🧪 Quick API Test</h5>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Test API Login</h6>
                            </div>
                            <div class="card-body">
                                <form id="quickTestForm">
                                    <div class="mb-3">
                                        <label class="form-label">Username:</label>
                                        <input type="text" class="form-control" id="quickUsername" value="admin">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Password:</label>
                                        <input type="password" class="form-control" id="quickPassword" value="admin123">
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-play-circle me-2"></i>Test Login API
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Results</h6>
                            </div>
                            <div class="card-body">
                                <div id="quickResults" style="min-height: 200px; font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-y: auto;">
                                    Click "Test Login API" to see results...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Common Issues -->
                <h5>⚠️ Common Issues & Solutions</h5>
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card border-danger h-100">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">1. JavaScript Errors</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Symptoms:</strong></p>
                                <ul class="small">
                                    <li>Button tidak respond</li>
                                    <li>Form tidak submit</li>
                                    <li>Loading terus</li>
                                </ul>
                                <p><strong>Check:</strong></p>
                                <ol class="small">
                                    <li>Press F12</li>
                                    <li>Go to Console tab</li>
                                    <li>Look for red errors</li>
                                    <li>Try login again</li>
                                </ol>
                                <button class="btn btn-danger btn-sm w-100" onclick="checkConsole()">
                                    Check Console
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-warning h-100">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">2. API Connection</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Symptoms:</strong></p>
                                <ul class="small">
                                    <li>Network errors</li>
                                    <li>CORS errors</li>
                                    <li>404 Not Found</li>
                                </ul>
                                <p><strong>Check:</strong></p>
                                <ol class="small">
                                    <li>XAMPP running?</li>
                                    <li>Database exists?</li>
                                    <li>API files accessible?</li>
                                </ol>
                                <a href="/api/login.php" target="_blank" class="btn btn-warning btn-sm w-100">
                                    Test API Direct
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-info h-100">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">3. React App Issues</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Symptoms:</strong></p>
                                <ul class="small">
                                    <li>State not updating</li>
                                    <li>Components not rendering</li>
                                    <li>Routing problems</li>
                                </ul>
                                <p><strong>Check:</strong></p>
                                <ol class="small">
                                    <li>React dev server running?</li>
                                    <li>Port 5173 accessible?</li>
                                    <li>No build errors?</li>
                                </ol>
                                <button class="btn btn-info btn-sm w-100" onclick="checkReactApp()">
                                    Check React App
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step by Step Debug -->
                <h5>🔍 Step-by-Step Debug Process</h5>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Debug Steps:</h6>
                                <ol>
                                    <li><strong>Test API First:</strong> Use form above to test if backend works</li>
                                    <li><strong>Check Browser Console:</strong> Press F12, look for errors</li>
                                    <li><strong>Check Network Tab:</strong> See if requests are being made</li>
                                    <li><strong>Verify React App:</strong> Make sure dev server is running</li>
                                    <li><strong>Check Credentials:</strong> Use correct demo accounts</li>
                                    <li><strong>Clear Cache:</strong> Hard refresh with Ctrl+F5</li>
                                </ol>
                            </div>
                            <div class="col-md-6">
                                <h6>Valid Demo Accounts:</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Role</th>
                                                <th>Username</th>
                                                <th>Password</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span class="badge bg-danger">Admin</span></td>
                                                <td><code>admin</code></td>
                                                <td><code>admin123</code></td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge bg-warning">Kasir</span></td>
                                                <td><code>kasir</code></td>
                                                <td><code>kasir123</code></td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge bg-info">Koki</span></td>
                                                <td><code>koki</code></td>
                                                <td><code>koki123</code></td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge bg-success">User</span></td>
                                                <td><code>user</code></td>
                                                <td><code>user123</code></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Fixes -->
                <h5 class="mt-4">🔧 Quick Fixes</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="bi bi-arrow-clockwise text-primary" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Restart React</h6>
                                <small class="text-muted">Stop (Ctrl+C) and run npm run dev</small>
                                <button class="btn btn-primary btn-sm mt-2" onclick="showRestartSteps()">
                                    How To
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="bi bi-trash text-warning" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Clear Cache</h6>
                                <small class="text-muted">Clear browser cache & localStorage</small>
                                <button class="btn btn-warning btn-sm mt-2" onclick="clearAllCache()">
                                    Clear Now
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="bi bi-database text-success" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Fix Database</h6>
                                <small class="text-muted">Check & repair database</small>
                                <a href="diagnose_auth.php" class="btn btn-success btn-sm mt-2" target="_blank">
                                    Fix DB
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="bi bi-terminal text-info" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Check Logs</h6>
                                <small class="text-muted">View error logs</small>
                                <button class="btn btn-info btn-sm mt-2" onclick="showLogInstructions()">
                                    View Logs
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Instructions -->
                <div class="mt-4 p-3 bg-primary text-white rounded">
                    <h6><i class="bi bi-lightbulb me-2"></i>Most Likely Solutions</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>If API test above works:</strong>
                            <ol class="mb-0">
                                <li>Problem is in React app</li>
                                <li>Check browser console for errors</li>
                                <li>Restart React dev server</li>
                                <li>Clear browser cache</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <strong>If API test fails:</strong>
                            <ol class="mb-0">
                                <li>Problem is in backend</li>
                                <li>Check XAMPP is running</li>
                                <li>Check database connection</li>
                                <li>Run database diagnosis</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const resultsDiv = document.getElementById('quickResults');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            resultsDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearLog() {
            resultsDiv.textContent = '';
        }
        
        // Test API directly
        document.getElementById('quickTestForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            clearLog();
            
            const username = document.getElementById('quickUsername').value;
            const password = document.getElementById('quickPassword').value;
            
            log(`🧪 Testing API login with ${username}/${password}...`);
            
            try {
                log('📡 Sending request to /api/login.php...');
                
                const response = await fetch('/api/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                log(`📊 Response status: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log('📦 Response data:');
                log(JSON.stringify(data, null, 2));
                
                if (data.success) {
                    log('✅ API LOGIN SUCCESSFUL!', 'success');
                    log(`👤 User: ${data.data.user.name}`, 'success');
                    log(`🎭 Role: ${data.data.user.role}`, 'success');
                    log(`🔑 Token: ${data.data.token.substring(0, 20)}...`, 'success');
                    
                    log('💾 Testing localStorage storage...');
                    localStorage.setItem('token', data.data.token);
                    localStorage.setItem('user', JSON.stringify(data.data.user));
                    log('✅ Auth data stored in localStorage', 'success');
                    
                    log('🎯 CONCLUSION: API works! Problem is in React app.', 'success');
                    log('🔧 NEXT STEPS:', 'warning');
                    log('1. Check browser console for React errors');
                    log('2. Restart React dev server');
                    log('3. Clear browser cache');
                    
                } else {
                    log('❌ API LOGIN FAILED!', 'error');
                    log(`💬 Error: ${data.message}`, 'error');
                    log('🔧 NEXT STEPS:', 'warning');
                    log('1. Check credentials are correct');
                    log('2. Check database has demo users');
                    log('3. Run database diagnosis');
                }
                
            } catch (error) {
                log('❌ REQUEST FAILED!', 'error');
                log(`💬 Error: ${error.message}`, 'error');
                log('🔧 POSSIBLE CAUSES:', 'warning');
                log('- XAMPP not running');
                log('- Database connection failed');
                log('- API file missing or has errors');
                log('- CORS issues');
                log('- Network connectivity problems');
            }
        });
        
        function checkConsole() {
            alert(`🔍 Check Browser Console for Errors:

📋 Steps:
1. Press F12 to open DevTools
2. Click on "Console" tab
3. Look for RED error messages
4. Go to your React login page
5. Try to login while console is open
6. Take screenshot of any errors

🚨 Common errors to look for:
- "Cannot read property of undefined"
- "Network request failed" 
- "CORS policy error"
- "404 Not Found"
- "500 Internal Server Error"
- "Uncaught TypeError"
- "Failed to fetch"

📸 If you see errors, copy them and send to me!`);
        }
        
        async function checkReactApp() {
            clearLog();
            log('🔍 Checking React app status...');
            
            try {
                const response = await fetch('http://localhost:5173/');
                if (response.ok) {
                    log('✅ React app is running on port 5173', 'success');
                    log('🌐 Try accessing: http://localhost:5173/login');
                } else {
                    log(`⚠️ React app responded with status: ${response.status}`, 'warning');
                }
            } catch (error) {
                log('❌ React app is not accessible: ' + error.message, 'error');
                log('🔧 Make sure to run: npm run dev', 'warning');
            }
        }
        
        function showRestartSteps() {
            alert(`🔄 Restart React App:

📋 Steps:
1. Go to terminal where React app is running
2. Press Ctrl+C to stop the app
3. Wait for "Process terminated" message
4. Run: npm run dev
5. Wait for "Local: http://localhost:5173" message
6. Go to http://localhost:5173/login
7. Try login again

🎯 This fixes most React issues:
- State management problems
- Routing issues  
- Component update problems
- Hot reload issues`);
        }
        
        function clearAllCache() {
            if (confirm('🗑️ This will clear all browser cache and reload. Continue?')) {
                // Clear localStorage
                localStorage.clear();
                sessionStorage.clear();
                
                // Clear cache and reload
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }
                
                // Hard reload
                location.reload(true);
            }
        }
        
        function showLogInstructions() {
            alert(`📋 Check Error Logs:

🖥️ Browser Console:
1. Press F12 → Console tab
2. Look for red errors
3. Try login again

📁 Server Logs:
1. Check XAMPP Control Panel
2. Click "Logs" for Apache
3. Look for recent errors

🗄️ Database Logs:
1. Check XAMPP Control Panel  
2. Click "Logs" for MySQL
3. Look for connection errors

📱 React Dev Tools:
1. Install React Developer Tools extension
2. Check Components tab
3. Look for state issues`);
        }
    </script>
</body>
</html>
