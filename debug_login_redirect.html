<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login Redirect Issues</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .log-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h3 class="mb-0">
                    <i class="bi bi-arrow-right-circle me-2"></i>
                    Debug Login Redirect Issues
                </h3>
            </div>
            <div class="card-body">
                
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Issue:</strong> Login berhasil tapi tidak redirect ke halaman yang benar
                </div>
                
                <!-- Test Login with Different Roles -->
                <h5>🧪 Test Login & Redirect</h5>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">Admin Test</h6>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-danger btn-sm w-100" onclick="testLogin('admin', 'admin123')">
                                    Login as Admin
                                </button>
                                <small class="text-muted">Should redirect to /admin</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">Kasir Test</h6>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-warning btn-sm w-100" onclick="testLogin('kasir', 'kasir123')">
                                    Login as Kasir
                                </button>
                                <small class="text-muted">Should redirect to /kasir</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">Koki Test</h6>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-info btn-sm w-100" onclick="testLogin('koki', 'koki123')">
                                    Login as Koki
                                </button>
                                <small class="text-muted">Should redirect to /koki</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="mb-0">User Test</h6>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-secondary btn-sm w-100" onclick="testLogin('user', 'user123')">
                                    Login as User
                                </button>
                                <small class="text-muted">Should redirect to /</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Check Current Auth Status -->
                <h5>🔍 Current Authentication Status</h5>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <button class="btn btn-primary w-100" onclick="checkAuthStatus()">
                            <i class="bi bi-person-check me-2"></i>Check Auth Status
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-secondary w-100" onclick="clearAuth()">
                            <i class="bi bi-trash me-2"></i>Clear Auth Data
                        </button>
                    </div>
                </div>
                
                <!-- Test Direct Navigation -->
                <h5>🎯 Test Direct Navigation</h5>
                <div class="row mb-4">
                    <div class="col-md-2">
                        <a href="http://localhost:5173/" class="btn btn-outline-primary btn-sm w-100" target="_blank">
                            Home (/)
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="http://localhost:5173/admin" class="btn btn-outline-danger btn-sm w-100" target="_blank">
                            Admin
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="http://localhost:5173/kasir" class="btn btn-outline-warning btn-sm w-100" target="_blank">
                            Kasir
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="http://localhost:5173/koki" class="btn btn-outline-info btn-sm w-100" target="_blank">
                            Koki
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="http://localhost:5173/login" class="btn btn-outline-success btn-sm w-100" target="_blank">
                            Login
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="http://localhost:5173/register" class="btn btn-outline-secondary btn-sm w-100" target="_blank">
                            Register
                        </a>
                    </div>
                </div>
                
                <!-- Results -->
                <h5>📊 Test Results</h5>
                <div id="results" class="log-box">
                    Click any test button to see results...
                </div>
                
                <!-- Common Issues & Solutions -->
                <h5>🔧 Common Issues & Solutions</h5>
                <div class="accordion" id="solutionsAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution1">
                                1. Login berhasil tapi tidak redirect
                            </button>
                        </h2>
                        <div id="solution1" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                            <div class="accordion-body">
                                <strong>Penyebab:</strong> JavaScript error atau routing issue<br>
                                <strong>Solusi:</strong>
                                <ul>
                                    <li>Buka browser console (F12) dan lihat error</li>
                                    <li>Pastikan React Router berfungsi dengan baik</li>
                                    <li>Cek apakah user data tersimpan di localStorage</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution2">
                                2. Redirect ke halaman yang salah
                            </button>
                        </h2>
                        <div id="solution2" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                            <div class="accordion-body">
                                <strong>Penyebab:</strong> Role tidak sesuai atau logic redirect salah<br>
                                <strong>Solusi:</strong>
                                <ul>
                                    <li>Cek role user di database</li>
                                    <li>Pastikan logic redirect di Login.jsx benar</li>
                                    <li>Verifikasi ProtectedRoute component</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution3">
                                3. Halaman tidak bisa diakses setelah login
                            </button>
                        </h2>
                        <div id="solution3" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                            <div class="accordion-body">
                                <strong>Penyebab:</strong> Authentication state tidak tersimpan<br>
                                <strong>Solusi:</strong>
                                <ul>
                                    <li>Cek localStorage untuk token dan user data</li>
                                    <li>Pastikan AuthContext berfungsi dengan baik</li>
                                    <li>Refresh halaman untuk reload auth state</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="mt-4">
                    <h6>🚀 Quick Actions</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <button class="btn btn-success btn-sm w-100" onclick="runFullTest()">
                                Run Full Test
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning btn-sm w-100" onclick="clearResults()">
                                Clear Results
                            </button>
                        </div>
                        <div class="col-md-3">
                            <a href="diagnose_auth.php" class="btn btn-info btn-sm w-100" target="_blank">
                                Full Diagnosis
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-danger btn-sm w-100" onclick="location.reload()">
                                Reload Page
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const resultsDiv = document.getElementById('results');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            resultsDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            if (type === 'error') {
                resultsDiv.className = 'log-box error';
            } else if (type === 'success') {
                resultsDiv.className = 'log-box success';
            } else if (type === 'warning') {
                resultsDiv.className = 'log-box warning';
            }
        }
        
        function clearResults() {
            resultsDiv.textContent = 'Results cleared...\n';
            resultsDiv.className = 'log-box';
        }
        
        async function testLogin(username, password) {
            log(`Testing login with ${username}/${password}...`);
            
            try {
                // Step 1: Test API login
                const response = await fetch('/api/login.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ API Login successful for ${username}`, 'success');
                    log(`User: ${data.data.user.name}, Role: ${data.data.user.role}`);
                    
                    // Step 2: Simulate localStorage storage
                    localStorage.setItem('token', data.data.token);
                    localStorage.setItem('user', JSON.stringify(data.data.user));
                    log(`✅ Auth data stored in localStorage`, 'success');
                    
                    // Step 3: Determine expected redirect
                    const role = data.data.user.role;
                    let expectedRedirect;
                    switch (role) {
                        case 'admin': expectedRedirect = '/admin'; break;
                        case 'kasir': expectedRedirect = '/kasir'; break;
                        case 'koki': expectedRedirect = '/koki'; break;
                        default: expectedRedirect = '/'; break;
                    }
                    
                    log(`Expected redirect for ${role}: ${expectedRedirect}`, 'info');
                    
                    // Step 4: Test if we can access the expected page
                    setTimeout(() => {
                        const testUrl = `http://localhost:5173${expectedRedirect}`;
                        log(`Testing access to: ${testUrl}`);
                        window.open(testUrl, '_blank');
                    }, 1000);
                    
                } else {
                    log(`❌ API Login failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Login test error: ${error.message}`, 'error');
            }
        }
        
        function checkAuthStatus() {
            log('Checking current authentication status...');
            
            const token = localStorage.getItem('token');
            const userStr = localStorage.getItem('user');
            
            if (token && userStr) {
                try {
                    const user = JSON.parse(userStr);
                    log(`✅ User is logged in: ${user.name} (${user.role})`, 'success');
                    log(`Token: ${token.substring(0, 20)}...`);
                    
                    // Test which pages should be accessible
                    const role = user.role;
                    log(`\nAccessible pages for ${role}:`);
                    
                    switch (role) {
                        case 'admin':
                            log('- /admin (Admin Dashboard)');
                            log('- /admin/kategori, /admin/menu, etc.');
                            break;
                        case 'kasir':
                            log('- /kasir (Kasir Dashboard)');
                            log('- /kasir/order, /kasir/order-detail');
                            break;
                        case 'koki':
                            log('- /koki (Koki Dashboard)');
                            log('- /koki/order-detail');
                            break;
                        default:
                            log('- / (Home page)');
                            log('- /kategori, /menu, /pelanggan, etc.');
                            break;
                    }
                    
                } catch (error) {
                    log(`❌ Error parsing user data: ${error.message}`, 'error');
                }
            } else {
                log(`❌ No authentication data found`, 'warning');
                log('User needs to login first');
            }
        }
        
        function clearAuth() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            log('✅ Authentication data cleared', 'success');
            log('User is now logged out');
        }
        
        async function runFullTest() {
            clearResults();
            log('Running comprehensive login & redirect test...\n');
            
            // Test 1: Check current auth status
            checkAuthStatus();
            log('\n' + '='.repeat(50) + '\n');
            
            // Test 2: Test each role
            const roles = [
                { username: 'admin', password: 'admin123' },
                { username: 'kasir', password: 'kasir123' },
                { username: 'koki', password: 'koki123' },
                { username: 'user', password: 'user123' }
            ];
            
            for (let i = 0; i < roles.length; i++) {
                const role = roles[i];
                log(`\nTesting ${role.username}...`);
                await testLogin(role.username, role.password);
                
                if (i < roles.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    clearAuth();
                    log(`Cleared auth for next test\n`);
                }
            }
            
            log('\n✅ Full test completed!', 'success');
        }
    </script>
</body>
</html>
