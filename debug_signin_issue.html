<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Sign In Issue</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h3 class="mb-0">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    Debug Sign In Issue
                </h3>
            </div>
            <div class="card-body">
                
                <div class="alert alert-warning">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Issue:</strong> Halaman login tampil tapi tombol Sign In tidak berfungsi
                </div>
                
                <!-- Step 1: Test API Directly -->
                <h5>🧪 Step 1: Test API Directly</h5>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Direct API Test</h6>
                            </div>
                            <div class="card-body">
                                <form id="apiTestForm">
                                    <div class="mb-3">
                                        <label class="form-label">Username:</label>
                                        <input type="text" class="form-control" id="testUsername" value="admin">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Password:</label>
                                        <input type="password" class="form-control" id="testPassword" value="admin123">
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-bug me-2"></i>Test API Login
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">API Response</h6>
                            </div>
                            <div class="card-body">
                                <div id="apiResults" style="min-height: 200px; font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-y: auto;">
                                    Click "Test API Login" to see results...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 2: Check React App -->
                <h5>🔍 Step 2: Check React App Status</h5>
                <div class="row mb-4">
                    <div class="col-md-4">
                        <button class="btn btn-info w-100" onclick="checkReactApp()">
                            <i class="bi bi-react me-2"></i>Check React App
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-warning w-100" onclick="checkConsoleErrors()">
                            <i class="bi bi-terminal me-2"></i>Check Console
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-success w-100" onclick="testNetworkRequests()">
                            <i class="bi bi-wifi me-2"></i>Test Network
                        </button>
                    </div>
                </div>
                
                <!-- Step 3: Common Issues -->
                <h5>⚠️ Step 3: Common Issues & Solutions</h5>
                <div class="accordion" id="issuesAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#issue1">
                                1. JavaScript Errors (Most Common)
                            </button>
                        </h2>
                        <div id="issue1" class="accordion-collapse collapse show" data-bs-parent="#issuesAccordion">
                            <div class="accordion-body">
                                <strong>Symptoms:</strong> Button tidak respond, form tidak submit<br>
                                <strong>Check:</strong>
                                <ol>
                                    <li>Buka browser console (F12)</li>
                                    <li>Lihat tab Console untuk error merah</li>
                                    <li>Refresh halaman dan coba login lagi</li>
                                </ol>
                                <strong>Common Errors:</strong>
                                <ul>
                                    <li>Cannot read property of undefined</li>
                                    <li>Network request failed</li>
                                    <li>CORS policy error</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue2">
                                2. API Connection Issues
                            </button>
                        </h2>
                        <div id="issue2" class="accordion-collapse collapse" data-bs-parent="#issuesAccordion">
                            <div class="accordion-body">
                                <strong>Symptoms:</strong> Loading terus, tidak ada response<br>
                                <strong>Check:</strong>
                                <ol>
                                    <li>XAMPP Apache & MySQL running</li>
                                    <li>Database apirestoran exists</li>
                                    <li>API files accessible</li>
                                </ol>
                                <strong>Test:</strong>
                                <ul>
                                    <li><a href="/api/login.php" target="_blank">Direct API access</a></li>
                                    <li><a href="diagnose_auth.php" target="_blank">Full diagnosis</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue3">
                                3. Form Validation Issues
                            </button>
                        </h2>
                        <div id="issue3" class="accordion-collapse collapse" data-bs-parent="#issuesAccordion">
                            <div class="accordion-body">
                                <strong>Symptoms:</strong> Form tidak submit, validation error<br>
                                <strong>Check:</strong>
                                <ol>
                                    <li>Username dan password terisi</li>
                                    <li>Tidak ada spasi di awal/akhir</li>
                                    <li>Gunakan credentials yang benar</li>
                                </ol>
                                <strong>Valid Credentials:</strong>
                                <ul>
                                    <li>admin / admin123</li>
                                    <li>kasir / kasir123</li>
                                    <li>koki / koki123</li>
                                    <li>user / user123</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 4: Quick Fixes -->
                <h5 class="mt-4">🔧 Step 4: Quick Fixes</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="bi bi-arrow-clockwise text-warning" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Restart React</h6>
                                <button class="btn btn-warning btn-sm" onclick="showRestartInstructions()">
                                    Instructions
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="bi bi-trash text-info" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Clear Cache</h6>
                                <button class="btn btn-info btn-sm" onclick="clearCache()">
                                    Clear Now
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="bi bi-database text-success" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Fix Database</h6>
                                <a href="diagnose_auth.php" class="btn btn-success btn-sm" target="_blank">
                                    Fix Now
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <i class="bi bi-bug text-danger" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">Debug Mode</h6>
                                <button class="btn btn-danger btn-sm" onclick="enableDebugMode()">
                                    Enable
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Instructions -->
                <div class="mt-4 p-3 bg-primary text-white rounded">
                    <h6><i class="bi bi-lightbulb me-2"></i>Step-by-Step Instructions</h6>
                    <ol class="mb-0">
                        <li><strong>Test API:</strong> Use the form above to test if API works</li>
                        <li><strong>Check Console:</strong> Press F12, look for red errors</li>
                        <li><strong>Try Credentials:</strong> Use admin/admin123 or other demo accounts</li>
                        <li><strong>Restart React:</strong> Stop (Ctrl+C) and run npm run dev again</li>
                        <li><strong>Clear Cache:</strong> Hard refresh with Ctrl+F5</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const resultsDiv = document.getElementById('apiResults');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            resultsDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearLog() {
            resultsDiv.textContent = '';
        }
        
        // Test API directly
        document.getElementById('apiTestForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            clearLog();
            
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;
            
            log(`Testing API login with ${username}/${password}...`);
            
            try {
                log('Sending request to /api/login.php...');
                
                const response = await fetch('/api/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                log(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log('Response received:');
                log(JSON.stringify(data, null, 2));
                
                if (data.success) {
                    log('✅ API LOGIN SUCCESSFUL!', 'success');
                    log(`User: ${data.data.user.name}`);
                    log(`Role: ${data.data.user.role}`);
                    log(`Token: ${data.data.token.substring(0, 20)}...`);
                    
                    // Test storing in localStorage
                    localStorage.setItem('token', data.data.token);
                    localStorage.setItem('user', JSON.stringify(data.data.user));
                    log('✅ Auth data stored in localStorage', 'success');
                    
                } else {
                    log('❌ API LOGIN FAILED!', 'error');
                    log(`Error: ${data.message}`);
                }
                
            } catch (error) {
                log('❌ REQUEST FAILED!', 'error');
                log(`Error: ${error.message}`);
                log('Possible causes:');
                log('- XAMPP not running');
                log('- Database connection failed');
                log('- API file missing or has errors');
                log('- CORS issues');
            }
        });
        
        async function checkReactApp() {
            log('Checking React app status...');
            try {
                const response = await fetch('http://localhost:5173/');
                if (response.ok) {
                    log('✅ React app is running on port 5173', 'success');
                } else {
                    log('⚠️ React app responded with status: ' + response.status, 'warning');
                }
            } catch (error) {
                log('❌ React app is not accessible: ' + error.message, 'error');
                log('Make sure to run: npm run dev');
            }
        }
        
        function checkConsoleErrors() {
            alert(`Check Browser Console for Errors:

1. Press F12 to open DevTools
2. Click on "Console" tab
3. Look for RED error messages
4. Try to login again while console is open
5. Take screenshot of any errors

Common errors to look for:
- "Cannot read property of undefined"
- "Network request failed" 
- "CORS policy error"
- "404 Not Found"
- "500 Internal Server Error"

If you see errors, copy them and send to me!`);
        }
        
        function testNetworkRequests() {
            alert(`Test Network Requests:

1. Press F12 to open DevTools
2. Click on "Network" tab
3. Try to login
4. Look for failed requests (red color)
5. Click on failed requests to see details

What to check:
- Is /api/login.php request being made?
- What is the response status code?
- Is there a response body?
- Are there any CORS errors?

This will help identify if the problem is:
- Frontend not sending request
- Backend not receiving request  
- API returning error`);
        }
        
        function showRestartInstructions() {
            alert(`Restart React App:

1. Go to terminal where React app is running
2. Press Ctrl+C to stop the app
3. Wait for it to stop completely
4. Run: npm run dev
5. Wait for "Local: http://localhost:5173" message
6. Go to http://localhost:5173/login
7. Try login again

This fixes most React-related issues including:
- State management problems
- Routing issues
- Component update problems`);
        }
        
        function clearCache() {
            if (confirm('This will clear browser cache and reload. Continue?')) {
                // Clear localStorage
                localStorage.clear();
                sessionStorage.clear();
                
                // Clear cache and reload
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }
                
                // Hard reload
                location.reload(true);
            }
        }
        
        function enableDebugMode() {
            // Add debug logging to localStorage
            localStorage.setItem('debug', 'true');
            
            alert(`Debug Mode Enabled!

This will:
1. Enable verbose logging in browser console
2. Show detailed error messages
3. Log all API requests and responses

To see debug info:
1. Press F12 to open console
2. Try login again
3. Look for detailed debug messages

To disable debug mode:
- Remove 'debug' from localStorage
- Or clear browser data`);
        }
    </script>
</body>
</html>
