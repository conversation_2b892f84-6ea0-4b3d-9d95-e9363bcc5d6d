<!DOCTYPE html>
<html>
<head>
    <title>Test Menu Upload</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h2>Test Menu Upload</h2>
    
    <form id="menuForm" enctype="multipart/form-data">
        <div class="form-group">
            <label for="menu">Nama Menu:</label>
            <input type="text" id="menu" name="menu" required>
        </div>
        
        <div class="form-group">
            <label for="deskripsi">Deskripsi:</label>
            <textarea id="deskripsi" name="deskripsi" rows="3" required></textarea>
        </div>
        
        <div class="form-group">
            <label for="harga">Harga:</label>
            <input type="number" id="harga" name="harga" required>
        </div>
        
        <div class="form-group">
            <label for="id_kategori">Kategori ID:</label>
            <select id="id_kategori" name="id_kategori" required>
                <option value="">Pilih Kategori</option>
                <option value="1">Makanan Utama</option>
                <option value="2">Snack</option>
                <option value="3">Minuman</option>
                <option value="4">Dessert</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="gambar">Gambar Menu:</label>
            <input type="file" id="gambar" name="gambar" accept="image/*">
        </div>
        
        <button type="submit">Tambah Menu</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('menuForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('http://localhost/project-react-resto/api/add_menu.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = '<div class="result success">' + result.message + '</div>';
                    this.reset();
                } else {
                    resultDiv.innerHTML = '<div class="result error">' + result.message + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="result error">Error: ' + error.message + '</div>';
            }
        });
    </script>
</body>
</html>
