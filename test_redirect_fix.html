<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test & Fix Login Redirect</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h3 class="mb-0">
                            <i class="bi bi-arrow-right-circle-fill me-2"></i>
                            Test & Fix Login Redirect
                        </h3>
                    </div>
                    <div class="card-body">
                        
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Masalah:</strong> Login berhasil tapi tidak redirect ke halaman yang benar
                        </div>
                        
                        <!-- Step 1: Quick Login Test -->
                        <h5>🚀 Step 1: Quick Login Test</h5>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-body">
                                        <h6 class="card-title">Test Login Admin</h6>
                                        <button class="btn btn-primary w-100" onclick="quickLogin('admin', 'admin123')">
                                            <i class="bi bi-person-gear me-2"></i>Login as Admin
                                        </button>
                                        <small class="text-muted">Should redirect to /admin</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-body">
                                        <h6 class="card-title">Test Login Kasir</h6>
                                        <button class="btn btn-warning w-100" onclick="quickLogin('kasir', 'kasir123')">
                                            <i class="bi bi-cash-coin me-2"></i>Login as Kasir
                                        </button>
                                        <small class="text-muted">Should redirect to /kasir</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 2: Manual Login Form -->
                        <h5>📝 Step 2: Manual Login Form</h5>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Login Form</h6>
                                    </div>
                                    <div class="card-body">
                                        <form id="manualLoginForm">
                                            <div class="mb-3">
                                                <label class="form-label">Username:</label>
                                                <select class="form-select" id="username">
                                                    <option value="admin">admin (Admin)</option>
                                                    <option value="kasir">kasir (Kasir)</option>
                                                    <option value="koki">koki (Koki)</option>
                                                    <option value="user">user (User)</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Password:</label>
                                                <input type="password" class="form-control" id="password" value="admin123">
                                            </div>
                                            <button type="submit" class="btn btn-success w-100">
                                                <i class="bi bi-box-arrow-in-right me-2"></i>Login & Test Redirect
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Login Results</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="loginResults" style="min-height: 200px; font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                                            Click login to see results...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 3: Direct Page Access Test -->
                        <h5>🎯 Step 3: Direct Page Access Test</h5>
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-body">
                                        <p>Test akses langsung ke halaman setelah login:</p>
                                        <div class="row">
                                            <div class="col-md-2">
                                                <button class="btn btn-outline-primary btn-sm w-100" onclick="testPageAccess('/')">
                                                    Home (/)
                                                </button>
                                            </div>
                                            <div class="col-md-2">
                                                <button class="btn btn-outline-danger btn-sm w-100" onclick="testPageAccess('/admin')">
                                                    Admin
                                                </button>
                                            </div>
                                            <div class="col-md-2">
                                                <button class="btn btn-outline-warning btn-sm w-100" onclick="testPageAccess('/kasir')">
                                                    Kasir
                                                </button>
                                            </div>
                                            <div class="col-md-2">
                                                <button class="btn btn-outline-info btn-sm w-100" onclick="testPageAccess('/koki')">
                                                    Koki
                                                </button>
                                            </div>
                                            <div class="col-md-2">
                                                <button class="btn btn-outline-success btn-sm w-100" onclick="testPageAccess('/login')">
                                                    Login
                                                </button>
                                            </div>
                                            <div class="col-md-2">
                                                <button class="btn btn-outline-secondary btn-sm w-100" onclick="clearAuth()">
                                                    Logout
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 4: Solutions -->
                        <h5>🔧 Step 4: Common Solutions</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="bi bi-arrow-clockwise text-info" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">Restart React App</h6>
                                        <p class="small text-muted">Stop (Ctrl+C) and run npm run dev again</p>
                                        <button class="btn btn-info btn-sm" onclick="showRestartInstructions()">
                                            Show Instructions
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="bi bi-browser-chrome text-warning" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">Clear Browser Cache</h6>
                                        <p class="small text-muted">Hard refresh or clear cache</p>
                                        <button class="btn btn-warning btn-sm" onclick="clearBrowserCache()">
                                            Clear Cache
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="bi bi-bug text-success" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">Check Console</h6>
                                        <p class="small text-muted">Open DevTools (F12) for errors</p>
                                        <button class="btn btn-success btn-sm" onclick="openDevTools()">
                                            Open DevTools
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Instructions -->
                        <div class="mt-4 p-3 bg-primary text-white rounded">
                            <h6><i class="bi bi-lightbulb me-2"></i>Instructions</h6>
                            <ol class="mb-0">
                                <li>First, test login with the buttons above</li>
                                <li>Check if you get redirected to the correct page</li>
                                <li>If not working, try the solutions below</li>
                                <li>Open browser console (F12) to see any errors</li>
                                <li>Make sure React app is running on localhost:5173</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const resultsDiv = document.getElementById('loginResults');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            resultsDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearLog() {
            resultsDiv.textContent = '';
        }
        
        async function quickLogin(username, password) {
            clearLog();
            log(`Quick login test: ${username}/${password}`);
            
            try {
                // Step 1: API Login
                const response = await fetch('/api/login.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Login successful!`, 'success');
                    log(`User: ${data.data.user.name}`);
                    log(`Role: ${data.data.user.role}`);
                    
                    // Step 2: Store auth data
                    localStorage.setItem('token', data.data.token);
                    localStorage.setItem('user', JSON.stringify(data.data.user));
                    log(`✅ Auth data stored`, 'success');
                    
                    // Step 3: Determine redirect URL
                    const role = data.data.user.role;
                    let redirectUrl;
                    switch (role) {
                        case 'admin': redirectUrl = '/admin'; break;
                        case 'kasir': redirectUrl = '/kasir'; break;
                        case 'koki': redirectUrl = '/koki'; break;
                        default: redirectUrl = '/'; break;
                    }
                    
                    log(`Expected redirect: ${redirectUrl}`);
                    
                    // Step 4: Test redirect
                    setTimeout(() => {
                        log(`Opening: http://localhost:5173${redirectUrl}`);
                        window.open(`http://localhost:5173${redirectUrl}`, '_blank');
                    }, 1000);
                    
                } else {
                    log(`❌ Login failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }
        
        // Auto-update password when username changes
        document.getElementById('username').addEventListener('change', function() {
            const username = this.value;
            const passwordField = document.getElementById('password');
            
            const passwords = {
                'admin': 'admin123',
                'kasir': 'kasir123',
                'koki': 'koki123',
                'user': 'user123'
            };
            
            passwordField.value = passwords[username] || 'admin123';
        });
        
        // Manual login form
        document.getElementById('manualLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            await quickLogin(username, password);
        });
        
        function testPageAccess(path) {
            log(`Testing access to: ${path}`);
            const url = `http://localhost:5173${path}`;
            window.open(url, '_blank');
        }
        
        function clearAuth() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            log('✅ Auth data cleared - User logged out', 'success');
        }
        
        function showRestartInstructions() {
            alert(`Restart React App Instructions:

1. Go to your terminal where React app is running
2. Press Ctrl+C to stop the app
3. Run: npm run dev
4. Wait for "Local: http://localhost:5173" message
5. Try login again

This fixes most routing and state issues.`);
        }
        
        function clearBrowserCache() {
            if (confirm('This will reload the page and clear cache. Continue?')) {
                // Clear localStorage
                localStorage.clear();
                // Hard reload
                location.reload(true);
            }
        }
        
        function openDevTools() {
            alert(`Open Browser DevTools:

1. Press F12 (or Ctrl+Shift+I)
2. Go to Console tab
3. Look for red error messages
4. Go to Network tab
5. Try login and check for failed requests

Common errors to look for:
- 404 errors (page not found)
- CORS errors
- JavaScript errors
- Failed API calls`);
        }
    </script>
</body>
</html>
