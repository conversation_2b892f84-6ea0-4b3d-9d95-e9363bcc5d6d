import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

const Register = () => {
  const [formData, setFormData] = useState({
    name: '',
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();
  const { register, loading, isAuthenticated } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError('Nama lengkap harus diisi');
      return false;
    }
    if (!formData.username.trim()) {
      setError('Username harus diisi');
      return false;
    }
    if (formData.username.length < 3) {
      setError('Username minimal 3 karakter');
      return false;
    }
    if (!formData.email.trim()) {
      setError('Email harus diisi');
      return false;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Format email tidak valid');
      return false;
    }
    if (!formData.password) {
      setError('Password harus diisi');
      return false;
    }
    if (formData.password.length < 6) {
      setError('Password minimal 6 karakter');
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      setError('Konfirmasi password tidak cocok');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!validateForm()) {
      return;
    }

    try {
      const { confirmPassword, ...dataToSend } = formData;
      const result = await register(dataToSend);

      if (result.success) {
        setSuccess('Registrasi berhasil! Silakan login.');
        setTimeout(() => navigate('/login'), 2000);
      } else {
        setError(result.error || 'Registrasi gagal');
      }
    } catch (err) {
      console.error('Registration error:', err);
      setError('Terjadi kesalahan saat registrasi');
    }
  };

  return (
    <div className="min-vh-100 d-flex align-items-center" style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <Container>
        <Row className="justify-content-center">
          <Col md={8} lg={7} xl={6}>
            <Card className="border-0 shadow-lg">
              <Card.Body className="p-5">
                {/* Header */}
                <div className="text-center mb-4">
                  <div className="mb-3">
                    <div className="bg-success bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center"
                         style={{ width: '80px', height: '80px' }}>
                      <i className="bi bi-person-plus text-white" style={{ fontSize: '2rem' }}></i>
                    </div>
                  </div>
                  <h2 className="fw-bold text-dark mb-2">Create Account</h2>
                  <p className="text-muted mb-0">Join our restaurant management system</p>
                </div>

                {/* Alerts */}
                {error && (
                  <Alert variant="danger" className="border-0 shadow-sm">
                    <i className="bi bi-exclamation-triangle me-2"></i>
                    {error}
                  </Alert>
                )}
                {success && (
                  <Alert variant="success" className="border-0 shadow-sm">
                    <i className="bi bi-check-circle me-2"></i>
                    {success}
                  </Alert>
                )}

                {/* Registration Form */}
                <Form onSubmit={handleSubmit}>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold text-dark">
                          <i className="bi bi-person me-2"></i>Full Name
                        </Form.Label>
                        <Form.Control
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          placeholder="Enter your full name"
                          className="form-control-lg border-0 shadow-sm"
                          style={{ backgroundColor: '#f8f9fa' }}
                          required
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold text-dark">
                          <i className="bi bi-at me-2"></i>Username
                        </Form.Label>
                        <Form.Control
                          type="text"
                          name="username"
                          value={formData.username}
                          onChange={handleChange}
                          placeholder="Choose a username"
                          className="form-control-lg border-0 shadow-sm"
                          style={{ backgroundColor: '#f8f9fa' }}
                          required
                        />
                        <Form.Text className="text-muted">
                          <small>Min 3 characters, letters, numbers, and underscore only</small>
                        </Form.Text>
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label className="fw-semibold text-dark">
                      <i className="bi bi-envelope me-2"></i>Email Address
                    </Form.Label>
                    <Form.Control
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="Enter your email address"
                      className="form-control-lg border-0 shadow-sm"
                      style={{ backgroundColor: '#f8f9fa' }}
                      required
                    />
                  </Form.Group>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold text-dark">
                          <i className="bi bi-lock me-2"></i>Password
                        </Form.Label>
                        <Form.Control
                          type="password"
                          name="password"
                          value={formData.password}
                          onChange={handleChange}
                          placeholder="Create a password"
                          className="form-control-lg border-0 shadow-sm"
                          style={{ backgroundColor: '#f8f9fa' }}
                          required
                        />
                        <Form.Text className="text-muted">
                          <small>Minimum 6 characters</small>
                        </Form.Text>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-4">
                        <Form.Label className="fw-semibold text-dark">
                          <i className="bi bi-shield-check me-2"></i>Confirm Password
                        </Form.Label>
                        <Form.Control
                          type="password"
                          name="confirmPassword"
                          value={formData.confirmPassword}
                          onChange={handleChange}
                          placeholder="Confirm your password"
                          className="form-control-lg border-0 shadow-sm"
                          style={{ backgroundColor: '#f8f9fa' }}
                          required
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Button
                    type="submit"
                    className="w-100 btn-lg fw-semibold border-0 shadow-sm"
                    style={{
                      background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
                    }}
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Creating Account...
                      </>
                    ) : (
                      <>
                        <i className="bi bi-person-check me-2"></i>
                        Create Account
                      </>
                    )}
                  </Button>
                </Form>

                {/* Divider */}
                <div className="text-center my-4">
                  <hr className="my-3" />
                  <span className="text-muted bg-white px-3">or</span>
                </div>

                {/* Login Link */}
                <div className="text-center">
                  <p className="text-muted mb-2">Already have an account?</p>
                  <Link
                    to="/login"
                    className="btn btn-outline-primary btn-lg fw-semibold w-100"
                  >
                    <i className="bi bi-box-arrow-in-right me-2"></i>
                    Sign In Instead
                  </Link>
                </div>

                {/* Info */}
                <div className="mt-4 p-3 bg-light rounded">
                  <div className="d-flex align-items-center">
                    <i className="bi bi-info-circle text-primary me-2"></i>
                    <small className="text-muted">
                      New accounts are created with <strong>User</strong> role by default.
                      Contact admin for role upgrades.
                    </small>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default Register;