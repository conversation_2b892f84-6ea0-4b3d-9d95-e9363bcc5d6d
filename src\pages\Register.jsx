import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Row, Col, Form, Button, Alert, Spinner } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

const Register = () => {
  const [formData, setFormData] = useState({
    name: '',
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();
  const { register, loading, isAuthenticated } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError('Nama lengkap harus diisi');
      return false;
    }
    if (!formData.username.trim()) {
      setError('Username harus diisi');
      return false;
    }
    if (formData.username.length < 3) {
      setError('Username minimal 3 karakter');
      return false;
    }
    if (!formData.email.trim()) {
      setError('Email harus diisi');
      return false;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Format email tidak valid');
      return false;
    }
    if (!formData.password) {
      setError('Password harus diisi');
      return false;
    }
    if (formData.password.length < 6) {
      setError('Password minimal 6 karakter');
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      setError('Konfirmasi password tidak cocok');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!validateForm()) {
      return;
    }

    try {
      const { confirmPassword, ...dataToSend } = formData;
      const result = await register(dataToSend);

      if (result.success) {
        setSuccess('Registrasi berhasil! Silakan login.');
        setTimeout(() => navigate('/login'), 2000);
      } else {
        setError(result.error || 'Registrasi gagal');
      }
    } catch (err) {
      console.error('Registration error:', err);
      setError('Terjadi kesalahan saat registrasi');
    }
  };

  return (
    <div style={{ backgroundColor: '#f8f9fa', minHeight: '100vh' }}>
      {/* Simple Header */}
      <div style={{ backgroundColor: 'white', padding: '60px 20px', borderBottom: '1px solid #e9ecef' }}>
        <div style={{ maxWidth: '600px', margin: '0 auto' }}>
          <div className="text-center">
            <h1 style={{ fontSize: '2.5rem', fontWeight: '300', color: '#333', marginBottom: '10px' }}>
              Create Account
            </h1>
            <p style={{ fontSize: '1.1rem', color: '#666', marginBottom: '0' }}>
              Join our restaurant management system
            </p>
          </div>
        </div>
      </div>

      {/* Registration Form Section */}
      <div style={{ padding: '60px 20px' }}>
        <div style={{ maxWidth: '600px', margin: '0 auto' }}>
          {/* Alerts */}
          {error && (
            <Alert variant="danger" style={{ border: 'none', borderRadius: '8px', marginBottom: '30px' }}>
              {error}
            </Alert>
          )}
          {success && (
            <Alert variant="success" style={{ border: 'none', borderRadius: '8px', marginBottom: '30px' }}>
              {success}
            </Alert>
          )}

          {/* Registration Form */}
          <div style={{ backgroundColor: 'white', padding: '40px', borderRadius: '12px', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
            <Form onSubmit={handleSubmit}>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label style={{ fontWeight: '500', color: '#333', marginBottom: '8px' }}>
                      Full Name
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Enter your full name"
                      style={{
                        padding: '12px 16px',
                        border: '1px solid #e9ecef',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label style={{ fontWeight: '500', color: '#333', marginBottom: '8px' }}>
                      Username
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      placeholder="Choose a username"
                      style={{
                        padding: '12px 16px',
                        border: '1px solid #e9ecef',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      required
                    />
                    <Form.Text style={{ fontSize: '0.85rem', color: '#666' }}>
                      Min 3 characters, letters, numbers, and underscore only
                    </Form.Text>
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mb-3">
                <Form.Label style={{ fontWeight: '500', color: '#333', marginBottom: '8px' }}>
                  Email Address
                </Form.Label>
                <Form.Control
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="Enter your email address"
                  style={{
                    padding: '12px 16px',
                    border: '1px solid #e9ecef',
                    borderRadius: '8px',
                    fontSize: '1rem'
                  }}
                  required
                />
              </Form.Group>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label style={{ fontWeight: '500', color: '#333', marginBottom: '8px' }}>
                      Password
                    </Form.Label>
                    <Form.Control
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      placeholder="Create a password"
                      style={{
                        padding: '12px 16px',
                        border: '1px solid #e9ecef',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      required
                    />
                    <Form.Text style={{ fontSize: '0.85rem', color: '#666' }}>
                      Minimum 6 characters
                    </Form.Text>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-4">
                    <Form.Label style={{ fontWeight: '500', color: '#333', marginBottom: '8px' }}>
                      Confirm Password
                    </Form.Label>
                    <Form.Control
                      type="password"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      placeholder="Confirm your password"
                      style={{
                        padding: '12px 16px',
                        border: '1px solid #e9ecef',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      required
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Button
                type="submit"
                style={{
                  width: '100%',
                  padding: '12px',
                  backgroundColor: '#28a745',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  fontWeight: '500'
                }}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Spinner
                      as="span"
                      animation="border"
                      size="sm"
                      role="status"
                      aria-hidden="true"
                      className="me-2"
                    />
                    Creating Account...
                  </>
                ) : (
                  'Create Account'
                )}
              </Button>
            </Form>

            {/* Login Link */}
            <div className="text-center mt-4">
              <p style={{ color: '#666', marginBottom: '15px' }}>Already have an account?</p>
              <Link
                to="/login"
                style={{
                  display: 'inline-block',
                  padding: '10px 20px',
                  color: '#007bff',
                  textDecoration: 'none',
                  border: '1px solid #007bff',
                  borderRadius: '8px',
                  fontWeight: '500'
                }}
              >
                Sign In Instead
              </Link>
            </div>
          </div>

          {/* Info */}
          <div style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '12px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            marginTop: '30px'
          }}>
            <div style={{
              padding: '20px',
              backgroundColor: '#e8f4fd',
              borderRadius: '8px',
              border: '1px solid #bee5eb'
            }}>
              <div style={{ fontSize: '0.95rem', color: '#0c5460' }}>
                <strong>Note:</strong> New accounts are created with <strong>User</strong> role by default.
                Contact admin for role upgrades.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;