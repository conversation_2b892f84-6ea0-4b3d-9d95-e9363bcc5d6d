import React, { useState, useEffect } from 'react';
import '../styles/Menu.css';

const MenuPage = () => {
  const [menus, setMenus] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeCategory, setActiveCategory] = useState('all');
  const [categories, setCategories] = useState([]);
  const [buyingItems, setBuyingItems] = useState(new Set());

  useEffect(() => {
    fetch("/api/get_menu.php")
      .then((res) => res.json())
      .then((data) => {
        if (data.success) {
          setMenus(data.data);
          // Ambil kategori unik dari data menu
          const uniqueCategories = [
            { id: 'all', name: 'Semua Menu' },
            ...Array.from(new Set(data.data.map(item => item.kategori_id))).map(id => {
              const item = data.data.find(menu => menu.kategori_id === id);
              return { id, name: item ? item.kategori_nama || `Kategori ${id}` : `Kategori ${id}` };
            })
          ];
          setCategories(uniqueCategories);
        } else {
          setError(data.message || "Gagal mengambil data menu");
        }
      })
      .catch((err) => {
        setError("Gagal mengambil data menu: " + err.message);
      })
      .finally(() => setLoading(false));
  }, []);

  // Fungsi untuk handle pembelian item
  const handleBuyItem = async (menu) => {
    // Set loading state untuk item ini
    setBuyingItems(prev => new Set([...prev, menu.id]));

    try {
      const response = await fetch('/api/add_order.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          menu_id: menu.id,
          nama_menu: menu.nama,
          harga: menu.harga,
          quantity: 1,
          customer_name: 'Guest User', // Bisa diganti dengan sistem login
          customer_phone: '', // Bisa ditambahkan form input
          notes: ''
        })
      });

      const result = await response.json();

      if (result.success) {
        alert(`✅ Berhasil menambahkan "${menu.nama}" ke pesanan!`);
      } else {
        alert(`❌ Gagal menambahkan pesanan: ${result.message}`);
      }
    } catch (error) {
      console.error('Error:', error);
      alert('❌ Terjadi kesalahan saat memproses pesanan');
    } finally {
      // Remove loading state
      setBuyingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(menu.id);
        return newSet;
      });
    }
  };

  const filteredMenus = activeCategory === 'all'
    ? menus
    : menus.filter(menu => menu.kategori_id === activeCategory);

  if (loading) {
    return <div className="text-center mt-5"><span>Loading...</span></div>;
  }
  if (error) {
    return <div className="alert alert-danger mt-4">{error}</div>;
  }

  return (
    <div style={{ backgroundColor: '#f8f9fa', minHeight: '100vh', padding: '20px' }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '30px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <h2 style={{ margin: 0, fontWeight: '300', color: '#333', fontSize: '2.5rem' }}>
            Menu Restoran
          </h2>
          <p style={{ color: '#666', marginTop: '10px' }}>
            Pilih menu favorit Anda
          </p>
        </div>

        {loading ? (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            fontSize: '16px',
            color: '#666'
          }}>
            Memuat menu...
          </div>
        ) : error ? (
          <div style={{
            backgroundColor: '#f8d7da',
            color: '#721c24',
            padding: '15px',
            borderRadius: '6px',
            border: '1px solid #f5c6cb',
            textAlign: 'center'
          }}>
            {error}
          </div>
        ) : (
          <>
            {/* Menu Categories */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              flexWrap: 'wrap',
              gap: '10px',
              marginBottom: '40px'
            }}>
              {categories.map(category => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: activeCategory === category.id ? '#007bff' : 'transparent',
                    color: activeCategory === category.id ? 'white' : '#007bff',
                    border: '1px solid #007bff',
                    borderRadius: '25px',
                    fontSize: '14px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (activeCategory !== category.id) {
                      e.target.style.backgroundColor = '#f8f9fa';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (activeCategory !== category.id) {
                      e.target.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  {category.name}
                </button>
              ))}
            </div>

            {/* Menu Cards */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
              gap: '20px'
            }}>
              {filteredMenus.map(menu => (
                <div key={menu.id} style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                  overflow: 'hidden',
                  transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px)';
                  e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                }}
                >
                  <div style={{ position: 'relative', overflow: 'hidden' }}>
                    <img
                      src={menu.image || 'https://via.placeholder.com/400x300/f5f5f5/999999?text=Tidak+Ada+Gambar'}
                      alt={menu.nama}
                      style={{
                        width: '100%',
                        height: '200px',
                        objectFit: 'cover'
                      }}
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = 'https://via.placeholder.com/400x300/f5f5f5/999999?text=Gambar+Tidak+Tersedia';
                      }}
                    />
                    {menu.badge && (
                      <div style={{
                        position: 'absolute',
                        top: '10px',
                        right: '10px',
                        backgroundColor: '#007bff',
                        color: 'white',
                        padding: '5px 10px',
                        borderRadius: '15px',
                        fontSize: '12px'
                      }}>
                        {menu.badge}
                      </div>
                    )}
                  </div>
                  <div style={{ padding: '20px' }}>
                    <h3 style={{
                      fontSize: '1.2rem',
                      fontWeight: '500',
                      color: '#333',
                      marginBottom: '10px',
                      margin: 0
                    }}>
                      {menu.nama}
                    </h3>
                    <p style={{
                      color: '#666',
                      fontSize: '0.9rem',
                      marginBottom: '15px',
                      lineHeight: '1.5'
                    }}>
                      {menu.deskripsi}
                    </p>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '15px'
                    }}>
                      <span style={{
                        fontSize: '1.1rem',
                        fontWeight: '600',
                        color: '#007bff'
                      }}>
                        Rp {parseInt(menu.harga).toLocaleString('id-ID')}
                      </span>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                        <span style={{ color: '#ffc107' }}>★★★★★</span>
                        <span style={{ fontSize: '0.9rem', color: '#666' }}>
                          {menu.rating || '4.5'}
                        </span>
                      </div>
                    </div>
                    <button
                      onClick={() => handleBuyItem(menu)}
                      disabled={buyingItems.has(menu.id)}
                      style={{
                        width: '100%',
                        padding: '10px',
                        backgroundColor: buyingItems.has(menu.id) ? '#6c757d' : '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        fontSize: '14px',
                        cursor: buyingItems.has(menu.id) ? 'not-allowed' : 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '8px'
                      }}
                    >
                      <span>🛒</span>
                      {buyingItems.has(menu.id) ? 'Memproses...' : 'Beli Sekarang'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default MenuPage;
