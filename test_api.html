<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Orders</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h3 {
            color: #555;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test API Orders System</h1>
        
        <h3>1. Test Add Order (POST)</h3>
        <button class="btn" onclick="testAddOrder()">🛒 Test Tambah Pesanan</button>
        <div id="addResult" class="result"></div>
        
        <h3>2. Test Get Orders (GET)</h3>
        <button class="btn" onclick="testGetOrders()">📋 Test Ambil Pesanan</button>
        <button class="btn" onclick="testGetOrdersByStatus('pending')">⏳ Test Filter Pending</button>
        <button class="btn" onclick="testGetOrdersByStatus('confirmed')">✅ Test Filter Confirmed</button>
        <div id="getResult" class="result"></div>
        
        <h3>3. Test Menu Integration</h3>
        <button class="btn" onclick="testGetMenu()">🍽️ Test Get Menu</button>
        <div id="menuResult" class="result"></div>
    </div>

    <script>
        // Base URL untuk API
        const API_BASE = '/project-react-resto/api';
        
        // Test Add Order
        async function testAddOrder() {
            const resultDiv = document.getElementById('addResult');
            resultDiv.textContent = 'Testing add order...';
            resultDiv.className = 'result';
            
            try {
                const testData = {
                    menu_id: 1,
                    nama_menu: 'Nasi Goreng Test',
                    harga: 25000,
                    quantity: 2,
                    customer_name: 'Test Customer',
                    customer_phone: '081234567890',
                    notes: 'Test order dari API'
                };
                
                const response = await fetch(`${API_BASE}/add_order.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = result.success ? 'result success' : 'result error';
                
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        // Test Get Orders
        async function testGetOrders() {
            const resultDiv = document.getElementById('getResult');
            resultDiv.textContent = 'Testing get orders...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`${API_BASE}/get_orders.php`);
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = result.success ? 'result success' : 'result error';
                
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        // Test Get Orders by Status
        async function testGetOrdersByStatus(status) {
            const resultDiv = document.getElementById('getResult');
            resultDiv.textContent = `Testing get orders with status: ${status}...`;
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`${API_BASE}/get_orders.php?status=${status}`);
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = result.success ? 'result success' : 'result error';
                
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        // Test Get Menu
        async function testGetMenu() {
            const resultDiv = document.getElementById('menuResult');
            resultDiv.textContent = 'Testing get menu...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`${API_BASE}/get_menu.php`);
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = result.success ? 'result success' : 'result error';
                
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        // Auto test on load
        window.onload = function() {
            console.log('🧪 API Test Page loaded');
            console.log('Click buttons to test different API endpoints');
        };
    </script>
</body>
</html>
