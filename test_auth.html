<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="bi bi-shield-check me-2"></i>
                            Authentication System Test
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="bi bi-database me-2"></i>Database Setup</h5>
                                <p>First, set up the users table with default accounts:</p>
                                <a href="api/setup_users_table.php" class="btn btn-warning mb-3" target="_blank">
                                    <i class="bi bi-gear me-2"></i>Setup Database
                                </a>
                                
                                <h5><i class="bi bi-person-check me-2"></i>Test Login</h5>
                                <p>Test the login functionality:</p>
                                <a href="src/pages/Login.jsx" class="btn btn-primary mb-3">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Go to Login Page
                                </a>
                                
                                <h5><i class="bi bi-person-plus me-2"></i>Test Registration</h5>
                                <p>Test the registration functionality:</p>
                                <a href="src/pages/Register.jsx" class="btn btn-success mb-3">
                                    <i class="bi bi-person-plus me-2"></i>Go to Register Page
                                </a>
                            </div>
                            
                            <div class="col-md-6">
                                <h5><i class="bi bi-key me-2"></i>Default Credentials</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Role</th>
                                                <th>Username</th>
                                                <th>Password</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span class="badge bg-danger">Admin</span></td>
                                                <td><code>admin</code></td>
                                                <td><code>admin123</code></td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge bg-warning">Kasir</span></td>
                                                <td><code>kasir</code></td>
                                                <td><code>kasir123</code></td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge bg-info">Koki</span></td>
                                                <td><code>koki</code></td>
                                                <td><code>koki123</code></td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge bg-secondary">User</span></td>
                                                <td><code>user</code></td>
                                                <td><code>user123</code></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <h5><i class="bi bi-api me-2"></i>API Endpoints</h5>
                                <div class="list-group">
                                    <a href="api/login.php" class="list-group-item list-group-item-action" target="_blank">
                                        <i class="bi bi-arrow-right me-2"></i>Login API
                                    </a>
                                    <a href="api/register.php" class="list-group-item list-group-item-action" target="_blank">
                                        <i class="bi bi-arrow-right me-2"></i>Register API
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-12">
                                <h5><i class="bi bi-list-check me-2"></i>What's Fixed</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">
                                                <i class="bi bi-check-circle text-success me-2"></i>
                                                Modern Bootstrap UI design
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-check-circle text-success me-2"></i>
                                                Consistent database schema
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-check-circle text-success me-2"></i>
                                                Improved API endpoints
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-check-circle text-success me-2"></i>
                                                Better error handling
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">
                                                <i class="bi bi-check-circle text-success me-2"></i>
                                                Role-based authentication
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-check-circle text-success me-2"></i>
                                                Password hashing security
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-check-circle text-success me-2"></i>
                                                Input validation
                                            </li>
                                            <li class="list-group-item">
                                                <i class="bi bi-check-circle text-success me-2"></i>
                                                Responsive design
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
